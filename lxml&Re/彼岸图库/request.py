"""
@Project     : Spider
<AUTHOR> shinkte
@Date        : 2025/8/16 20:51
@Version     : 1.0
@Description : 
"""

import urllib3
import requests


urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


if __name__ == '__main__':
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "cache-control": "max-age=0",
        "if-modified-since": "Sat, 16 Aug 2025 03:03:20 GMT",
        "if-none-match": "W/\"689ff4f8-28f6\"",
        "priority": "u=2",
        "referer": "https://pic.netbian.com/4kyouxi/index_2.html",
        "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "script",
        "sec-fetch-mode": "no-cors",
        "sec-fetch-site": "same-origin",
        "sec-fetch-user": "?1",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Referer": "https://pic.netbian.com/static/css/style.css?20250708",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Origin": "https://pic.netbian.com",
        "Referer;": ""
    }
    cookies = {
        "RcGFvecookieclassrecord": "%2C54%2C53%2C55%2CRcGFvecookieclassrecord=%2C54%2C53%2C55%2C"
    }
    url = "https://pic.netbian.com/4kyouxi/index_2.html"
    response = requests.get(url, headers=headers, cookies=cookies,verify=False)

    print(response.text)
    print(response)
