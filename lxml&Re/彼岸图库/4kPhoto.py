import requests
from lxml import etree
import urllib3  # 禁用安全请求警告,当目标使用htpps时使用
import os


# 忽略SSL证书验证
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# 解决爬取网页出现中文乱码的情况
def rebuilt_Language(url, headers):
    response = requests.get(url=url, headers=headers, verify=False)
    response.encoding = response.apparent_encoding
    return response


if __name__ == "__main__":
    # UA伪装
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36'
    }
    # 建立一个文件夹存储照片
    i = -1
    if not os.path.exists('./picLibs'):
        os.mkdir('./picLibs')
    # 设置一个通用的url
    src_list = []  # 存储图片的src
    img_name_list = []  # 存储图片的名字
    for pageNum in range(2, 3):
        new_url = f"https://pic.netbian.com/4kmeinv/index_{pageNum}.html"
        page_text = rebuilt_Language(url=new_url, headers=headers).text
        print(page_text)
        # tree = etree.HTML(page_text)

