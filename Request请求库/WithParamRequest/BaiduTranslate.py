"""
@Project     : Spider
<AUTHOR> shinkte
@Date        : 2025/8/15 14:40
@Version     : 1.0
@Description : 
"""

import requests
import json

if __name__ == '__main__':
    post_url = "https://fanyi.baidu.com/sug"
    # UA伪装  :让爬虫身份标识伪装成浏览器
    header = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    word = input("请输入要查询的单词：")
    params = {
        "kw": word
    }
    # 发送请求
    # post请求返回数据格式： Json
    responses = requests.post(url=post_url, headers=header, data=params)
    # 将响应的json数据解析为Python字典对象
    dic_obj=responses.json()

    # 持久化存储
    fileName=word+".json"
    fp=open(fileName,"w",encoding="utf-8")
    # 将字典对象以json格式写入文件
    json.dump(dic_obj,fp=fp,ensure_ascii=False)

    print("over!")
