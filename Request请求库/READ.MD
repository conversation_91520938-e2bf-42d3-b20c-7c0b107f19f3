# Python基础知识

## 基本数据结构

### 字典

字典是一种可变容器模型，可以存储任意类型对象。且键必须是唯一的，但值则不必。
```python
d = {key1 : value1, key2 : value2, key3 : value3 }
```
**常见用法**
```python
   1 创建字典
     dict={}
  2 添加键值对
     dict[key]=value
  3 删除键值对
     del dict[key]
  4 修改键值对
     dict[key]=value
  5 查询键值对
     dict[key]
  6 遍历字典
     for key in dict:
        print(key, dict[key])
  7 判断键是否存在
     if key in dict:
       print("key exists")
  8 获取字典的长度
     len(dict)
     
```        