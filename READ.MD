# Python基础知识

## 基本数据结构

### 字典

字典是一种可变容器模型，可以存储任意类型对象。且键必须是唯一的，但值则不必。

```python
d = {key1 : value1, key2 : value2, key3 : value3 }
```

**常见用法**

```python
   1 创建字典
     dict={}
  2 添加键值对
     dict[key]=value
  3 删除键值对
     del dict[key]
  4 修改键值对
     dict[key]=value
  5 查询键值对
     dict[key]
  6 遍历字典
     for key in dict:
        print(key, dict[key])
  7 判断键是否存在
     if key in dict:
       print("key exists")
  8 获取字典的长度
     len(dict)
   
```

### 列表

列表总得每个值都有对应的位置值（索引）。
```python
list = ['red', 'green', 'blue', 'yellow', 'white', 'black']
```
![img.png](img.png)
**特点**
1. 索引从下标0开始
2. 列表可以存储任意类型的数据
3. 列表可以存储重复的数据
4. 列表是可变的，可以修改列表中的数据
5. 列表可以嵌套
6. 列表可以使用切片操作
7. 列表可以使用in操作符判断元素是否存在

**常见用法**
```python


```